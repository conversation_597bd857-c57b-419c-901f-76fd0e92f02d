const tableId = 1
const propertyItemList = [
  {
    tableId,
    schemaId: 1,
    items: ['北京', '上海', '广州'],
  },
  {
    tableId,
    schemaId: 2,
    items: [
      { name: '页面浏览', value: 'pageview' },
      { name: '登录', value: 'login' },
      { name: '注册', value: 'register' },
      { name: '点击', value: 'click' },
      { name: '购买', value: 'pay' },
      { name: '下单', value: 'order' },
    ],
  },
  {
    tableId,
    schemaId: 4,
    items: [20, 80, 40],
  },
]
const propertyList = [
  {
    field: 'bool',
    fieldType: 'BOOL',
    tableId: 31,
    schemaId: 4621,
    level1: '通用事件属性',
    level2: '',
    fieldName: '布尔',
    isEnum: false,
  },
  {
    field: 'long',
    fieldType: 'LONG',
    tableId: 31,
    schemaId: 4552,
    level1: '通用事件属性',
    level2: '',
    fieldName: 'long',
    isEnum: false,
  },
  {
    field: 'day',
    fieldType: 'TIMESTAMP',
    tableId: 31,
    schemaId: 3814,
    level1: '通用事件属性',
    level2: '',
    fieldName: 'day',
    isEnum: false,
  },
  {
    field: 'eventName',
    fieldType: 'STRING',
    tableId: 31,
    schemaId: 8,
    level1: '通用事件属性',
    level2: '',
    fieldName: '事件名称',
    isEnum: false,
  },
  {
    field: 'plugin',
    fieldType: 'STRING',
    tableId: 31,
    schemaId: 18,
    level1: '通用事件属性',
    level2: '',
    fieldName: '插件',
    isEnum: false,
  },
  {
    field: 'region',
    fieldType: 'STRING',
    tableId: 31,
    schemaId: 21,
    level1: '通用事件属性',
    level2: '',
    fieldName: '地区',
    isEnum: false,
  },
  {
    field: 'lgt',
    fieldType: 'DOUBLE',
    tableId: 31,
    schemaId: 16,
    level1: '通用事件属性',
    level2: '',
    fieldName: '经度',
    isEnum: false,
  },
  {
    field: 'lat',
    fieldType: 'DOUBLE',
    tableId: 31,
    schemaId: 15,
    level1: '通用事件属性',
    level2: '',
    fieldName: '纬度',
    isEnum: false,
  },
  {
    field: 'uaOs',
    fieldType: 'STRING',
    tableId: 31,
    schemaId: 31,
    level1: '通用事件属性',
    level2: '',
    fieldName: 'ua操作系统',
    isEnum: false,
  },
  {
    field: 'userId',
    fieldType: 'STRING',
    tableId: 31,
    schemaId: 36,
    level1: '通用事件属性',
    level2: '',
    fieldName: '用户ID',
    isEnum: false,
  },
  {
    field: 'userAgent',
    fieldType: 'STRING',
    tableId: 31,
    schemaId: 35,
    level1: '通用事件属性',
    level2: '',
    fieldName: '用户代理',
    isEnum: false,
  },
  {
    field: 'uaName',
    fieldType: 'STRING',
    tableId: 31,
    schemaId: 30,
    level1: '通用事件属性',
    level2: '',
    fieldName: 'ua名称',
    isEnum: false,
  },
  {
    field: 'title',
    fieldType: 'STRING',
    tableId: 31,
    schemaId: 26,
    level1: '通用事件属性',
    level2: '',
    fieldName: '标题',
    isEnum: false,
  },
  {
    field: 'url',
    fieldType: 'STRING',
    tableId: 31,
    schemaId: 34,
    level1: '通用事件属性',
    level2: '',
    fieldName: 'url',
    isEnum: false,
  },
  {
    field: 'language',
    fieldType: 'STRING',
    tableId: 31,
    schemaId: 14,
    level1: '通用事件属性',
    level2: '',
    fieldName: '语言',
    isEnum: false,
  },
  {
    field: 'eventTime',
    fieldType: 'TIMESTAMP',
    tableId: 31,
    schemaId: 10,
    level1: '通用事件属性',
    level2: '',
    fieldName: '事件时间',
    isEnum: false,
  },
  {
    field: 'userTags',
    fieldType: 'STRING',
    tableId: 31,
    schemaId: 37,
    level1: '通用事件属性',
    level2: '',
    fieldName: '用户标签',
    isEnum: false,
  },
  {
    field: 'sessionId',
    fieldType: 'STRING',
    tableId: 31,
    schemaId: 24,
    level1: '通用事件属性',
    level2: '',
    fieldName: '会话ID',
    isEnum: false,
  },
  {
    field: 'netType',
    fieldType: 'STRING',
    tableId: 31,
    schemaId: 17,
    level1: '通用事件属性',
    level2: '',
    fieldName: '网络类型',
    isEnum: false,
  },
  {
    field: 'key',
    fieldType: 'STRING',
    tableId: 31,
    schemaId: 13,
    level1: '通用事件属性',
    level2: '',
    fieldName: '值',
    isEnum: false,
  },
  {
    field: 'deviceId',
    fieldType: 'STRING',
    tableId: 31,
    schemaId: 6,
    level1: '通用事件属性',
    level2: '',
    fieldName: '压力_设备ID',
    isEnum: false,
  },
  {
    field: 'country',
    fieldType: 'STRING',
    tableId: 31,
    schemaId: 5,
    level1: '通用事件属性',
    level2: '',
    fieldName: '国家',
    isEnum: true,
  },
  {
    field: 'date',
    fieldType: 'STRING',
    tableId: 31,
    schemaId: 38,
    level1: '通用事件属性',
    level2: '',
    fieldName: '日期',
    isEnum: false,
  },
  {
    field: 'city',
    fieldType: 'STRING',
    tableId: 31,
    schemaId: 3,
    level1: '通用事件属性',
    level2: '',
    fieldName: '城市',
    isEnum: false,
  },
  {
    field: 'projectId',
    fieldType: 'STRING',
    tableId: 31,
    schemaId: 39,
    level1: '通用事件属性',
    level2: '',
    fieldName: '通用_项目ID',
    isEnum: false,
  },
  {
    field: 'i_var0',
    fieldType: 'INT',
    tableId: 31,
    level1: '事件专有属性',
    level2: '',
    fieldName: '年龄',
    isEnum: false,
  },
  {
    field: 's_var0',
    fieldType: 'STRING',
    tableId: 31,
    level1: '事件专有属性',
    level2: '',
    fieldName: '哈哈哈',
    isEnum: false,
  },
]
const eventDatas = {
  header: { code: 0 },
  body: {
    content: [
      {
        createTime: 1754198689000,
        updateTime: 1754198695000,
        createUserId: 2172,
        updateUserId: 2172,
        createUserName: '郑国磊',
        updateUserName: '郑国磊',
        projectId: 'fRrqcHdyBkVNfOeN',
        id: 333,
        name: '公金',
        eventNameValue: '公金',
        specialPropertyMappingList: [
          {
            displayName: '公金',
            propertySchema: '公金',
            dataType: 'TIMESTAMP',
            index: 0,
          },
        ],
        eventType: 'BURIED_POINT_EVENT',
        remark: '公金',
        qualityCheck: 0,
        checkError: 0,
      },
      {
        createTime: 1751196605000,
        updateTime: 1751196609000,
        createUserId: 9,
        updateUserId: 9,
        createUserName: 'Alan.gao',
        updateUserName: 'Alan.gao',
        projectId: 'fRrqcHdyBkVNfOeN',
        id: 331,
        name: '薪资到账',
        eventNameValue: '薪资到账',
        specialPropertyMappingList: [],
        eventType: 'BURIED_POINT_EVENT',
        qualityCheck: 0,
        checkError: 0,
      },
      {
        createTime: 1751191429000,
        updateTime: 1751191432000,
        createUserId: 9,
        updateUserId: 9,
        createUserName: 'Alan.gao',
        updateUserName: 'Alan.gao',
        projectId: 'fRrqcHdyBkVNfOeN',
        id: 330,
        name: '首次发薪入账',
        eventNameValue: '转账',
        specialPropertyMappingList: [],
        eventType: 'BURIED_POINT_EVENT',
        qualityCheck: 0,
        checkError: 0,
      },
      {
        createTime: 1751188388000,
        updateTime: 1751188396000,
        createUserId: 9,
        updateUserId: 9,
        createUserName: 'Alan.gao',
        updateUserName: 'Alan.gao',
        projectId: 'fRrqcHdyBkVNfOeN',
        id: 328,
        name: '社保资金转入',
        eventNameValue: '转账',
        specialPropertyMappingList: [],
        eventType: 'BURIED_POINT_EVENT',
        qualityCheck: 0,
        checkError: 0,
      },
      {
        createTime: 1751182908000,
        updateTime: 1751182915000,
        createUserId: 9,
        updateUserId: 9,
        createUserName: 'Alan.gao',
        updateUserName: 'Alan.gao',
        projectId: 'fRrqcHdyBkVNfOeN',
        id: 327,
        name: '已完成代发借记卡签约和账户激活',
        eventNameValue: '办理业务',
        specialPropertyMappingList: [],
        eventType: 'BURIED_POINT_EVENT',
        qualityCheck: 0,
        checkError: 0,
      },
      {
        createTime: 1749781965000,
        updateTime: 1749781965000,
        createUserId: 2119,
        updateUserId: 2119,
        createUserName: '刘彦博',
        updateUserName: '刘彦博',
        projectId: 'fRrqcHdyBkVNfOeN',
        id: 325,
        name: '节假日同户名跨行转账',
        eventNameValue: '节假日同户名跨行转账',
        specialPropertyMappingList: [],
        eventType: 'BURIED_POINT_EVENT',
        level1: '',
        level2: '',
        remark: '',
        checkError: 0,
      },
      {
        createTime: 1748327111000,
        updateTime: 1748327116000,
        createUserId: 9,
        updateUserId: 9,
        createUserName: 'Alan.gao',
        updateUserName: 'Alan.gao',
        projectId: 'fRrqcHdyBkVNfOeN',
        id: 324,
        name: '完成任务',
        eventNameValue: '绑定银行卡',
        specialPropertyMappingList: [],
        eventType: 'BURIED_POINT_EVENT',
        qualityCheck: 0,
        checkError: 0,
      },
      {
        createTime: 1748254812000,
        updateTime: 1748254815000,
        createUserId: 9,
        updateUserId: 9,
        createUserName: 'Alan.gao',
        updateUserName: 'Alan.gao',
        projectId: 'fRrqcHdyBkVNfOeN',
        id: 323,
        name: '是否有意向',
        eventNameValue: '绑定银行卡',
        specialPropertyMappingList: [],
        eventType: 'BURIED_POINT_EVENT',
        qualityCheck: 0,
        checkError: 0,
      },
      {
        createTime: 1748254753000,
        updateTime: 1748254755000,
        createUserId: 9,
        updateUserId: 9,
        createUserName: 'Alan.gao',
        updateUserName: 'Alan.gao',
        projectId: 'fRrqcHdyBkVNfOeN',
        id: 322,
        name: '是否接通电话',
        eventNameValue: '申请银行卡',
        specialPropertyMappingList: [],
        eventType: 'BURIED_POINT_EVENT',
        qualityCheck: 0,
        checkError: 0,
      },
      {
        createTime: 1748253801000,
        updateTime: 1748253803000,
        createUserId: 9,
        updateUserId: 9,
        createUserName: 'Alan.gao',
        updateUserName: 'Alan.gao',
        projectId: 'fRrqcHdyBkVNfOeN',
        id: 321,
        name: '购买指定产品',
        eventNameValue: '购买产品A',
        specialPropertyMappingList: [],
        eventType: 'BURIED_POINT_EVENT',
        qualityCheck: 0,
        checkError: 0,
      },
      {
        createTime: 1748253765000,
        updateTime: 1748253767000,
        createUserId: 9,
        updateUserId: 9,
        createUserName: 'Alan.gao',
        updateUserName: 'Alan.gao',
        projectId: 'fRrqcHdyBkVNfOeN',
        id: 320,
        name: '完成风险评估',
        eventNameValue: '绑定银行卡',
        specialPropertyMappingList: [],
        eventType: 'BURIED_POINT_EVENT',
        qualityCheck: 0,
        checkError: 0,
      },
      {
        createTime: 1748253720000,
        updateTime: 1748253724000,
        createUserId: 9,
        updateUserId: 9,
        createUserName: 'Alan.gao',
        updateUserName: 'Alan.gao',
        projectId: 'fRrqcHdyBkVNfOeN',
        id: 319,
        name: '完成一键绑卡任务',
        eventNameValue: '绑定银行卡',
        specialPropertyMappingList: [],
        eventType: 'BURIED_POINT_EVENT',
        qualityCheck: 0,
        checkError: 0,
      },
      {
        createTime: 1716170215000,
        updateTime: 1748252537000,
        createUserId: 9,
        updateUserId: 9,
        createUserName: 'Alan.gao',
        updateUserName: 'Alan.gao',
        projectId: 'fRrqcHdyBkVNfOeN',
        id: 282,
        name: '大额还款未提款',
        eventNameValue: '大额还款未提款',
        specialPropertyMappingList: [
          {
            displayName: '当日累计还款金额',
            propertySchema: '店铺收入',
            dataType: 'INT',
            index: 2,
          },
        ],
        eventType: 'BURIED_POINT_EVENT',
        remark: '',
        qualityCheck: 0,
        checkError: 0,
      },
      {
        createTime: 1669813801000,
        updateTime: 1748251890000,
        createUserId: 47,
        updateUserId: 9,
        createUserName: '任波',
        updateUserName: 'Alan.gao',
        projectId: 'fRrqcHdyBkVNfOeN',
        id: 153,
        name: '领取权益',
        eventNameValue: '完成任务',
        specialPropertyMappingList: [
          {
            displayName: '权益名称1',
            propertySchema: '权益名称',
            dataType: 'STRING',
            index: 0,
          },
        ],
        eventType: 'BURIED_POINT_EVENT',
        remark: '',
        qualityCheck: 0,
        checkError: 0,
      },
      {
        createTime: 1702522412000,
        updateTime: 1746674485000,
        createUserId: 9,
        updateUserId: 9,
        createUserName: 'Alan.gao',
        updateUserName: 'Alan.gao',
        projectId: 'fRrqcHdyBkVNfOeN',
        id: 269,
        name: '产生复购',
        eventNameValue: '产生复购',
        specialPropertyMappingList: [
          {
            displayName: '城市',
            propertySchema: 'city',
            dataType: 'STRING',
            index: 0,
          },
        ],
        eventType: 'BURIED_POINT_EVENT',
        remark: '',
        checkError: 0,
      },
      {
        createTime: 1740044430000,
        updateTime: 1745473626000,
        createUserId: 144,
        updateUserId: 2014,
        createUserName: '杨益平',
        updateUserName: '苗世军',
        projectId: 'fRrqcHdyBkVNfOeN',
        id: 309,
        name: '产品到期提醒',
        eventNameValue: 'order_expire',
        specialPropertyMappingList: [
          {
            displayName: '到期天数',
            propertySchema: 'num',
            dataType: 'STRING',
            index: 0,
          },
        ],
        eventType: 'BURIED_POINT_EVENT',
        level1: '',
        level2: '',
        checkError: 0,
      },
      {
        createTime: 1742265145000,
        updateTime: 1742265145000,
        createUserId: 47,
        updateUserId: 47,
        createUserName: '任波',
        updateUserName: '任波',
        projectId: 'fRrqcHdyBkVNfOeN',
        id: 312,
        name: '12',
        eventNameValue: '120000          ',
        specialPropertyMappingList: [],
        eventType: 'BURIED_POINT_EVENT',
        checkError: 0,
      },
      {
        createTime: 1740991020000,
        updateTime: 1740991020000,
        createUserId: 65,
        updateUserId: 65,
        createUserName: '谢洪旭',
        updateUserName: '谢洪旭',
        projectId: 'fRrqcHdyBkVNfOeN',
        id: 310,
        name: '接听且有意向客户名单',
        eventNameValue: 'call_ok',
        specialPropertyMappingList: [
          {
            displayName: '接听时间',
            propertySchema: 'receive_time',
            dataType: 'STRING',
            index: 0,
          },
        ],
        eventType: 'BURIED_POINT_EVENT',
        checkError: 0,
      },
      {
        createTime: 1730350434000,
        updateTime: 1730350434000,
        createUserId: 9,
        updateUserId: 9,
        createUserName: 'Alan.gao',
        updateUserName: 'Alan.gao',
        projectId: 'fRrqcHdyBkVNfOeN',
        id: 303,
        name: '信用卡申请开卡',
        eventNameValue: 'apply_cart',
        specialPropertyMappingList: [],
        eventType: 'BURIED_POINT_EVENT',
        checkError: 0,
      },
      {
        createTime: 1724126629000,
        updateTime: 1724126629000,
        createUserId: 9,
        updateUserId: 9,
        createUserName: 'Alan.gao',
        updateUserName: 'Alan.gao',
        projectId: 'fRrqcHdyBkVNfOeN',
        id: 293,
        name: '登录APP活跃木易',
        eventNameValue: '登录APP活跃',
        specialPropertyMappingList: [],
        eventType: 'BURIED_POINT_EVENT',
        checkError: 0,
      },
    ],
    number: 0,
    size: 20,
    totalElements: 83,
  },
}
const functions = {
  header: { code: 0 },
  body: [
    {
      name: 'ABS',
      title: 'ABS',
      usage: 'ABS(数值)',
      description: 'Returns the absolute value of the numeric value',
      sample: '> SELECT abs(-1);\n 1',
      createSql: 'ABS()',
    },
    {
      name: 'APPROX_COUNT_DISTINCT',
      title: 'APPROX_COUNT_DISTINCT',
      usage: 'APPROX_COUNT_DISTINCT(expr[, relativeSD])',
      description:
        'Returns the estimated cardinality by HyperLogLog++. relativeSD defines the maximum relative standard deviation allowed.',
      sample: 'SELECT approx_count_distinct(col1) FROM VALUES (1), (1), (2), (2), (3) tab(col1);\n 3',
      createSql: 'APPROX_COUNT_DISTINCT()',
    },
    {
      name: 'AVG',
      title: '平均值',
      usage: 'AVG(表达式/数值字段)',
      description: '返回表达式或数值字段所有值的平均值，只适用于数值字段，空值不会计算',
      sample: 'AVG(销售额)，返回"销售额"字段对应的所有非空值的平均值',
      createSql: 'AVG()',
    },
    {
      name: 'BASE64_DECODE',
      title: 'BASE64_DECODE',
      usage: 'unbase64(表达式/字段)',
      description: '返回base64解码值',
      sample: 'UNBASE64([字段])，返回base64解码值',
      createSql: 'UNBASE64()',
    },
    {
      name: 'BASE64_ENCODE',
      title: 'BASE64_ENCODE',
      usage: 'BASE64(字段)',
      description: '返回base64解码值',
      sample: 'BASE64([字段])，返回base64解码值',
      createSql: 'BASE64()',
    },
    {
      name: 'CAST_TO',
      title: '转为其它类型',
      usage: 'CAST((表达式/字段 AS 类型)',
      description: '转化表达式/字段为相应类型，类型INT BIGINT DOUBLE等',
      sample: 'CAST([金额] AS INT)',
      createSql: 'CAST( AS )',
    },
    {
      name: 'CAST_TO_INT',
      title: '转为整型',
      usage: 'CAST((表达式/字段 AS INT)',
      description: '转化表达式/字段为整形数字',
      sample: 'CAST([金额] AS INT)',
      createSql: 'CAST( AS INT)',
    },
    {
      name: 'CEIL',
      title: 'CEIL',
      usage: 'CEIL(数值)',
      description: '返回不小于数值a的最小整数',
      sample: 'CEIL(4.12),返回5',
      createSql: 'CEIL()',
    },
    {
      name: 'COLLECT_LIST',
      title: '转集合列表',
      usage: 'COLLECT_LIST(表达式/字段)',
      description: '收集字段或表达式为不唯一的数组',
      sample: 'COLLECT_LIST([城市])',
      createSql: 'COLLECT_LIST()',
    },
    {
      name: 'CONCAT',
      title: '连接',
      usage: 'concat(col1, col2, ..., colN)',
      description: '连接字符型字段/表达式, 返回连接后的字符串',
      sample: 'select CONCAT([姓], \'先生\')',
      createSql: 'CONCAT()',
    },
    {
      name: 'COUNT',
      title: '计数',
      usage: 'COUNT(表达式/字段)',
      description: '返回表达式所有有效字段的数据条目数，空值不会计算',
      sample: 'COUNT(销售额)，返回"销售额"字段对应的所有非空值的数据条目数',
      createSql: 'COUNT()',
    },
    {
      name: 'CURRENT_DATE',
      title: 'CURRENT_DATE',
      usage: 'CURRENT_DATE()',
      description: 'Returns the current date at the start of query evaluation.',
      sample: '> SELECT current_date();\n 2020-04-25',
      createSql: 'CURRENT_DATE()',
    },
    {
      name: 'CURRENT_TIMESTAMP',
      title: 'CURRENT_TIMESTAMP',
      usage: 'CURRENT_TIMESTAMP()',
      description: 'Returns the current timestamp at the start of query evaluation',
      sample: '> SELECT current_timestamp();\n 2020-04-25 15:49:11.914',
      createSql: 'CURRENT_TIMESTAMP()',
    },
    {
      name: 'DATEDIFF',
      title: 'DATEDIFF',
      usage: 'DATEDIFF(日期字段1,日期字段2)',
      description: '返回两个日期相差的天数，只允许传入日期型字段',
      sample: 'DATEDIFF([离职日期],[入职日期] )，返回同一行上"离职日期"至"入职日期"间隔天数',
      createSql: 'DATEDIFF()',
    },
    {
      name: 'DATE_ADD',
      title: 'DATE_ADD',
      usage: 'DATE_ADD(起始日期, 数值字段)',
      description: '返回从起始日期算起，数值字段对应天数之后的日期',
      sample: 'DATE_ADD([入库日期], 1)，返回货品入库第二天的日期',
      createSql: 'DATE_ADD()',
    },
    {
      name: 'DATE_FORMAT',
      title: 'DATE_FORMAT',
      usage: 'DATE_FORMAT(timestamp, fmt)',
      description: 'Converts timestamp to a value of string in the format specified by the date format fmt.',
      sample: '> SELECT date_format(\'2016-04-08\', \'y\');\n 2016',
      createSql: 'DATE_FORMAT()',
    },
    {
      name: 'DATE_PART',
      title: 'DATE_PART',
      usage: 'DATE_PART(field, source)',
      description: 'Extracts a part of the date/timestamp or interval source.',
      sample:
        '> SELECT date_part(\'YEAR\', TIMESTAMP \'2019-08-12 01:00:00.123456\');\n 2019\n > SELECT date_part(\'week\', timestamp\'2019-08-12 01:00:00.123456\'); \n 33 \n > SELECT date_part(\'doy\', DATE\'2019-08-12\'); \n 224 \n > SELECT date_part(\'SECONDS\', timestamp\'2019-10-01 00:00:01.000001\'); \n 1.000001 \n > SELECT date_part(\'days\', interval 1 year 10 months 5 days); \n 5 \n > SELECT date_part(\'seconds\', interval 5 hours 30 seconds 1 milliseconds 1 microseconds); \n 30.001001',
      createSql: 'DATE_PART()',
    },
    {
      name: 'DATE_SUB',
      title: 'DATE_SUB',
      usage: 'DATE_SUB(起始日期, 数值字段)',
      description: '返回从起始日期算起，数值字段对应天数之前的日期',
      sample: 'DATE_SUB([出库日期], 1)，返回货品出库前一天的日期',
      createSql: 'DATE_SUB()',
    },
    {
      name: 'DATE_TRUNC',
      title: 'DATE_TRUNC',
      usage: 'DATE_TRUNC(fmt, ts)',
      description: 'Returns timestamp ts truncated to the unit specified by the format model fmt.',
      sample:
        '> SELECT date_trunc(\'YEAR\', \'2015-03-05T09:32:05.359\');\n 2015-01-01 00:00:00\n > SELECT date_trunc(\'MM\', \'2015-03-05T09:32:05.359\');\n 2015-03-01 00:00:00\n > SELECT date_trunc(\'DD\', \'2015-03-05T09:32:05.359\');\n 2015-03-05 00:00:00\n > SELECT date_trunc(\'HOUR\', \'2015-03-05T09:32:05.359\');\n 2015-03-05 09:00:00\n > SELECT date_trunc(\'MILLISECOND\', \'2015-03-05T09:32:05.123456\');\n 2015-03-05 09:32:05.123',
      createSql: 'DATE_TRUNC()',
    },
    {
      name: 'DAY',
      title: 'DAY',
      usage: 'DAY(日期字段)',
      description: '返回该日期对应的日的值。只允许传入日期型字段',
      sample: 'DAY([下单时间])，返回该行"下单时间"字段对应的日的值',
      createSql: 'DAY()',
    },
    {
      name: 'DAYOFMONTH',
      title: 'DAYOFMONTH',
      usage: 'DAYOFMONTH(date)',
      description: 'Returns the day of month of the date/timestamp.',
      sample: 'SELECT dayofmonth(‘2009-07-30’); 30',
      createSql: 'DAYOFMONTH()',
    },
    {
      name: 'DAYOFYEAR',
      title: 'DAYOFYEAR',
      usage: 'DAYOFYEAR(date)',
      description: 'Returns the day of year of the date/timestamp.',
      sample: 'SELECT dayofyear(\'2016-04-09\');\n 100',
      createSql: 'DAYOFYEAR()',
    },
    {
      name: 'DAY_OF_WEEK',
      title: 'DAY_OF_WEEK',
      usage: 'DAY_OF_WEEK(日期字段[, 类型])',
      description: '返回该日期在一周中的第几天,如果需要返回“星期一”的格式，第二参数传入0',
      sample:
        'DAY_OF_WEEK([下单时间])；DAY_OF_WEEK(\'2016-05-13\')，返回5；DAY_OF_WEEK([下单时间]，0)；DAY_OF_WEEK(\'2016-05-13\'，0)，返回星期五',
      createSql: 'DAYOFMONTH()',
    },
    {
      name: 'DISTINCT',
      title: '去重',
      usage: 'DISTINCT(表达式/字段)',
      description: '返回唯一值',
      sample: 'DISTINCT([字段])',
      createSql: 'DISTINCT()',
    },
    {
      name: 'EXP',
      title: 'EXP',
      usage: 'EXP(expr)',
      description: 'Returns e to the power of expr',
      sample: '> SELECT exp(0)',
      createSql: 'EXP()',
    },
    { name: 'EXPLODE', title: '数组炸开', usage: '', description: '', sample: '', createSql: 'EXPLODE(表达式/字段)' },
    {
      name: 'FIRST',
      title: '第一个',
      usage: 'FIRST(表达式/字段)',
      description: '返回第一个值',
      sample: 'FIRST([字段])',
      createSql: 'FIRST()',
    },
    {
      name: 'FLOOR',
      title: 'FLOOR',
      usage: 'FLOOR(数值)',
      description: '返回不大于数值a的最大整数',
      sample: 'FLOOR(4.12),返回4',
      createSql: 'FLOOR()',
    },
    {
      name: 'FROM_UNIXTIME',
      title: '格式化unix时间',
      usage: 'from_unixtime(unix_time, format)',
      description: 'Returns unix_time in the specified format',
      sample: '> SELECT from_unixtime(0, \'yyyy-MM-dd HH:mm:ss\');\n 1969-12-31 16:00:00',
      createSql: 'from_unixtime( , \'yyyy-MM-dd HH:mm:ss\')',
    },
    {
      name: 'FUNNEL_COUNT',
      title: '漏斗计数',
      usage: 'FUNNEL_COUNT(表达式/字段)',
      description: '',
      sample: '',
      createSql: 'FUNNEL_COUNT()',
    },
    {
      name: 'HOUR',
      title: 'HOUR',
      usage: 'HOUR(日期字段)',
      description: '返回该日期对应的小时的值。只允许传入日期型字段',
      sample: 'HOUR([下单时间])，返回该行"下单时间"字段对应的小时的值',
      createSql: 'HOUR()',
    },
    {
      name: 'IF',
      title: 'IF',
      usage: 'IF(表达式，结果1，结果2)',
      description: 'IF为判断函数，表达式为比较型或计算型语句。若表达式的计算结果正确，则返回"结果1"，否则，返回"结果2"',
      sample:
        'IF([订单数] &gt; 500, "合格", "不合格")。结果为若该行"订单数"字段对应值大于500，则返回"合格"，否则返回"不合格"',
      createSql: 'IF()',
    },
    {
      name: 'INSTR',
      title: 'INSTR',
      usage: 'INSTR(str, substr)',
      description: 'Returns the (1-based) index of the first occurrence of substr in str',
      sample: '> select instr(\'datatist\', \'tist\'); 5',
      createSql: 'INSTR()',
    },
    {
      name: 'LAST',
      title: 'LAST',
      usage: 'LAST(expr[, isIgnoreNull])',
      description:
        'Returns the last value of expr for a group of rows. If isIgnoreNull is true, returns only non-null values',
      sample:
        '> SELECT last_value(col) FROM VALUES (10), (5), (20) AS tab(col); \n 20 \n > SELECT last_value(col) FROM VALUES (10), (5), (NULL) AS tab(col); \n NULL \n > SELECT last_value(col, true) FROM VALUES (10), (5), (NULL) AS tab(col); \n 5',
      createSql: 'LAST()',
    },
    {
      name: 'LAST_DAY_OF_MONTH',
      title: 'LAST_DAY_OF_MONTH',
      usage: 'LAST_DAY(日期字段)',
      description: '返回某月最后一天，函数参数为数值型字段，依次代表年，月',
      sample: 'LAST_DAY(\'2009-01-12\')，返回2009-01-31',
      createSql: 'LAST_DAY()',
    },
    {
      name: 'LENGTH',
      title: 'LENGTH',
      usage: 'LENGTH(字符串)',
      description: '返回字符串的长度',
      sample: 'LENGTH([货品名])，返回货品名的长度',
      createSql: 'LENGTH()',
    },
    {
      name: 'LN',
      title: 'LN',
      usage: 'LN(数值)',
      description: '求数值对数，ln(数值)',
      sample: 'LN(10),返回2.3025',
      createSql: 'LN()',
    },
    {
      name: 'LOG',
      title: 'LOG',
      usage: 'LOG(数值A,数值B)',
      description: '以A为底，求B的对数',
      sample: 'LOG(2,4),返回2.0',
      createSql: 'LOG()',
    },
    {
      name: 'LOWER',
      title: 'LOWER',
      usage: 'LOWER(表达式/字段)',
      description: '返回表达式或字段值全部小写形式的字符串',
      sample: 'LOWER("ABC")，返回"abc"',
      createSql: 'LOWER()',
    },
    {
      name: 'MAP',
      title: '生成映射',
      usage: 'map(key0, value0, key1, value1, ...)',
      description: '创建key value映射',
      sample: '',
      createSql: 'MAP()',
    },
    {
      name: 'MAX',
      title: '最大',
      usage: 'MAX(销售额)',
      description: '返回表达式或数值字段所有值的最大值，只适用于数值字段，空值不会计算',
      sample: 'MAX(销售额)',
      createSql: 'MAX()',
    },
    {
      name: 'MIN',
      title: '最小',
      usage: 'MIN(销售额)',
      description: '返回表达式或数值字段所有值的最大值，只适用于数值字段，空值不会计算',
      sample: 'MIN(销售额)',
      createSql: 'MIN()',
    },
    {
      name: 'MINUTE',
      title: 'MINUTE',
      usage: 'MINUTE(timestamp)',
      description: 'Returns the minute component of the string/timestamp.',
      sample: '> SELECT minute(\'2009-07-30 12:58:59\'); \n 58',
      createSql: 'MINUTE()',
    },
    {
      name: 'MONTH',
      title: 'MONTH',
      usage: 'month(date)',
      description: 'Returns the month component of the date/timestamp.',
      sample: '> SELECT month(\'2016-07-30\'); \n 7',
      createSql: 'MONTH()',
    },
    {
      name: 'NOW',
      title: 'NOW',
      usage: 'NOW()',
      description: 'Returns the current timestamp at the start of query evaluation.',
      sample: '> SELECT now(); \n 2020-04-25 15:49:11.914',
      createSql: 'NOW()',
    },
    {
      name: 'NVL',
      title: '空值判断',
      usage: 'NVL(表达式1/字段1, 表达式2/字段2)',
      description: '当表达式1/字段1为空时返回表达式2/字段2, 否则返回表达式1/字段1',
      sample: 'NVL([姓名], \'Sophia\')',
      createSql: 'NVL()',
    },
    {
      name: 'NVL2',
      title: 'NVL2',
      usage: 'nvl2(expr1, expr2, expr3)',
      description: 'Returns expr2 if expr1 is not null, or expr3 otherwise',
      sample: '> SELECT nvl2(NULL, 2, 1);\n1',
      createSql: 'NVL2()',
    },
    {
      name: 'POW',
      title: 'POW',
      usage: 'POW(数值A,数值B)',
      description: '求数值A的数值B次方',
      sample: 'POW(4，2),返回16.0',
      createSql: 'POW()',
    },
    {
      name: 'RAND',
      title: 'RAND',
      usage: 'RAND()',
      description: '返回大于0小于1的随机小数',
      sample: 'RAND(),返回随机数',
      createSql: 'RAND()',
    },
    {
      name: 'REGEXP_EXTRACT',
      title: 'REGEXP_EXTRACT',
      usage: 'REGEXP_EXTRACT(字符串, 正则表达式[, 索引])',
      description:
        '返回字符串正则表达式解析结果,需要注意的是正则表达式的需要使用\\进行转义，即\\d)，\'索引\'是返回结果(0表示返回全部结果，1表示返回正则表达式中第一个() 对应的结果)',
      sample: 'REGEXP_EXTRACT([货品ID],\'[\\d+\\-]+\', 0)，返回货品ID中的数字部分',
      createSql: 'REGEXP_EXTRACT()',
    },
    {
      name: 'REGEXP_REPLACE',
      title: 'REGEXP_REPLACE',
      usage: 'REGEXP_REPLACE(字符串A, 正则表达式, 字符串B)',
      description: '返回将字符串A中符合正则表达式的部分替换成字符串B后的结果',
      sample: 'REGEXP_REPLACE([货品名], \'[\\d＋]+\', \'\')，返回将货品名中数字部分替换成空字符串后的结果',
      createSql: 'REGEXP_REPLACE()',
    },
    {
      name: 'REPEAT',
      title: 'REPEAT',
      usage: 'REPEAT(字符串, 数值)',
      description: '返回字符串重复对应数值次数后的新字符串结果',
      sample: 'REPEAT([货品名], 2)，返回货品名重复2次得到字符串',
      createSql: 'REPEAT()',
    },
    {
      name: 'REVERSE',
      title: 'REVERSE',
      usage: 'REVERSE(字符串)',
      description: '返回字符串倒转后的新字符串结果',
      sample: 'REVERSE([类型编号])，返回类型编号倒转后的字符串',
      createSql: 'REVERSE()',
    },
    {
      name: 'ROUND',
      title: 'ROUND',
      usage: 'ROUND(数值A[,整数D])',
      description: '返回数值A四舍五入到小数点后D位。不填时为0',
      sample: 'ROUND(4.12，1),返回4.1',
      createSql: 'ROUND()',
    },
    {
      name: 'SLICE',
      title: '数组切分',
      usage: 'slice(x, start, length)',
      description: '取数组的子数组',
      sample: '',
      createSql: 'SLICE()',
    },
    {
      name: 'SPLIT',
      title: 'SPLIT',
      usage: 'SPLIT(str, regex, limit)',
      description: 'Splits str around occurrences that match regex and returns an array with a length of at most limit',
      sample:
        '> SELECT split(\'oneAtwoBthreeC\', \'[ABC]\'); \n ["one","two","three",""] \n > SELECT split(\'oneAtwoBthreeC\', \'[ABC]\', -1); \n ["one","two","three",""] \n > SELECT split(\'oneAtwoBthreeC\', \'[ABC]\', 2); \n ["one","twoBthreeC"]',
      createSql: 'SPLIT()',
    },
    {
      name: 'SQRT',
      title: 'SQRT',
      usage: 'SQRT(数值)',
      description: '求数值的根号，需要大于等于零',
      sample: 'SQRT(4),返回2.0',
      createSql: 'SQRT()',
    },
    {
      name: 'SUBSTR',
      title: 'SUBSTR',
      usage: 'SUBSTR(字符串, 起始位置[, 长度])',
      description: '返回从起始位置起对应长度的字符串的子字符串，长度为可选项',
      sample: 'SUBSTR([商品类型], 4)，返回商品类型的索引为4起至末尾的子字符串',
      createSql: 'SUBSTR()',
    },
    {
      name: 'SUM',
      title: '求和',
      usage: 'SUM(表达式/数值字段)',
      description: '返回表达式或数值字段所有值的和，只适用于数值字段，空值不会计算',
      sample: 'SUM(销售额)',
      createSql: 'SUM()',
    },
    {
      name: 'TO_DATE',
      title: 'TO_DATE',
      usage: 'TO_DATE(date_str[, fmt])',
      description:
        'Parses the date_str expression with the fmt expression to a date. Returns null with invalid input. By default, it follows casting rules to a date if the fmt is omitted.',
      sample:
        '> SELECT to_date(\'2009-07-30 04:17:52\');\n 2009-07-30 \n > SELECT to_date(\'2016-12-31\', \'yyyy-MM-dd\'); \n 2016-12-31',
      createSql: 'TO_DATE()',
    },
    {
      name: 'TRIM',
      title: 'TRIM',
      usage: 'TRIM(表达式/字段)',
      description: '去除表达式或字段中数据两边的空格',
      sample: 'TRIM(" ABC "),返回"ABC"',
      createSql: 'TRIM()',
    },
    {
      name: 'UNIQUE_COUNT',
      title: '唯一计数',
      usage: 'COUNT(DISTINCT(表达式/字段))',
      description: '去重计数，返回表达式所有有效字段的不同数据条目数，空值不会计算',
      sample: 'COUNT(DISTINCT(销售额))，返回"销售额"字段对应的所有非空值的不同数据条目数',
      createSql: 'COUNT(DISTINCT())',
    },
    {
      name: 'UNIX_TIMESTAMP',
      title: '转时间戳',
      usage: 'UNIX_TIMESTAMP(表达式/字段)',
      description: '转化时间类型的字段为时间戳',
      sample: 'UNIX_TIMESTAMP([生日])',
      createSql: 'UNIX_TIMESTAMP()',
    },
    {
      name: 'UPPER',
      title: 'UPPER',
      usage: 'UPPER(表达式/字段串)',
      description: '返回表达式或字段值全部大写形式的字符串',
      sample: 'UPPER("abc"),返回"ABC"',
      createSql: 'UPPER()',
    },
    {
      name: 'WEEKDAY',
      title: 'WEEKDAY',
      usage: 'WEEKDAY(date)',
      description: 'Returns the day of the week for date/timestamp (0 = Monday, 1 = Tuesday, ..., 6 = Sunday).',
      sample: '> SELECT weekday(\'2009-07-30\'); \n 3',
      createSql: 'WEEKDAY()',
    },
    {
      name: 'WEEKOFYEAR',
      title: 'WEEKOFYEAR',
      usage: 'WEEKOFYEAR(date)',
      description:
        'Returns the week of the year of the given date. A week is considered to start on a Monday and week 1 is the first week with >3 days.',
      sample: 'SELECT weekofyear(\'2008-02-20\'); \n 8',
      createSql: 'WEEKOFYEAR()',
    },
    {
      name: 'YEAR',
      title: 'YEAR',
      usage: 'YEAR(日期字段)',
      description: '返回该日期对应的年份。只允许传入日期型字段',
      sample: 'YEAR([下单时间])，返回该行"下单时间"字段对应的年份',
      createSql: 'YEAR()',
    },
  ],
}
const eventProperty = {
  header: { code: 0 },
  body: {
    content: [
      {
        createTime: 1612149900000,
        updateTime: 1612149900000,
        createUserId: 1,
        updateUserId: 1,
        createUserName: 'admin',
        updateUserName: 'admin',
        projectId: 'qvAD1jk8q0hA0Oxm',
        id: 284,
        displayName: '事件名称',
        eventId: 0,
        tableSchema: {
          createTime: 1579057522000,
          updateTime: 1590130558000,
          createUserId: 1,
          updateUserId: 1,
          projectId: '0',
          id: 8,
          table: {
            createTime: 1579056849000,
            updateTime: 1618213380000,
            createUserId: 1,
            updateUserId: 1,
            projectId: '0',
            id: 31,
            tableType: 'HIVE',
            name: 'event',
            displayName: '全局事件表',
            database: 'wolf',
            doc: '_doc',
            primaryKeys: [],
            parititionColumns: ['date', 'projectId'],
            memo: '',
            draft: true,
          },
          name: 'eventName',
          displayName: 'eventName',
          dataType: 'STRING',
        },
      },
      {
        createTime: 1591176144000,
        updateTime: 1604887714000,
        createUserId: 1,
        updateUserId: 1,
        createUserName: 'admin',
        updateUserName: 'admin',
        projectId: '0',
        id: 84,
        displayName: '城市',
        eventId: 0,
        tableSchema: {
          createTime: 1579057522000,
          updateTime: 1600745946000,
          createUserId: 1,
          updateUserId: 1,
          projectId: '0',
          id: 3,
          table: {
            createTime: 1579056849000,
            updateTime: 1618213380000,
            createUserId: 1,
            updateUserId: 1,
            projectId: '0',
            id: 31,
            tableType: 'HIVE',
            name: 'event',
            displayName: '全局事件表',
            database: 'wolf',
            doc: '_doc',
            primaryKeys: [],
            parititionColumns: ['date', 'projectId'],
            memo: '',
            draft: true,
          },
          name: 'city',
          displayName: '城市',
          dataType: 'STRING',
        },
      },
      {
        createTime: 1591176166000,
        updateTime: 1604887714000,
        createUserId: 1,
        updateUserId: 1,
        createUserName: 'admin',
        updateUserName: 'admin',
        projectId: '0',
        id: 85,
        displayName: 'app版本',
        eventId: 0,
        tableSchema: {
          createTime: 1579057522000,
          updateTime: 1600745915000,
          createUserId: 1,
          updateUserId: 1,
          projectId: '0',
          id: 1,
          table: {
            createTime: 1579056849000,
            updateTime: 1618213380000,
            createUserId: 1,
            updateUserId: 1,
            projectId: '0',
            id: 31,
            tableType: 'HIVE',
            name: 'event',
            displayName: '全局事件表',
            database: 'wolf',
            doc: '_doc',
            primaryKeys: [],
            parititionColumns: ['date', 'projectId'],
            memo: '',
            draft: true,
          },
          name: 'appVersion',
          displayName: 'app版本',
          dataType: 'STRING',
        },
      },
      {
        createTime: 1591176216000,
        updateTime: 1604887714000,
        createUserId: 1,
        updateUserId: 1,
        createUserName: 'admin',
        updateUserName: 'admin',
        projectId: '0',
        id: 86,
        displayName: '渠道ID1',
        eventId: 0,
        tableSchema: {
          createTime: 1579057522000,
          updateTime: 1600745932000,
          createUserId: 1,
          updateUserId: 1,
          projectId: '0',
          id: 2,
          table: {
            createTime: 1579056849000,
            updateTime: 1618213380000,
            createUserId: 1,
            updateUserId: 1,
            projectId: '0',
            id: 31,
            tableType: 'HIVE',
            name: 'event',
            displayName: '全局事件表',
            database: 'wolf',
            doc: '_doc',
            primaryKeys: [],
            parititionColumns: ['date', 'projectId'],
            memo: '',
            draft: true,
          },
          name: 'channelId',
          displayName: '渠道Id',
          dataType: 'INT',
        },
      },
      {
        createTime: 1591176240000,
        updateTime: 1604887714000,
        createUserId: 1,
        updateUserId: 1,
        createUserName: 'admin',
        updateUserName: 'admin',
        projectId: '0',
        id: 87,
        displayName: '通用属性continentCode',
        eventId: 0,
        tableSchema: {
          createTime: 1579057522000,
          updateTime: 1590130558000,
          createUserId: 1,
          updateUserId: 1,
          projectId: '0',
          id: 4,
          table: {
            createTime: 1579056849000,
            updateTime: 1618213380000,
            createUserId: 1,
            updateUserId: 1,
            projectId: '0',
            id: 31,
            tableType: 'HIVE',
            name: 'event',
            displayName: '全局事件表',
            database: 'wolf',
            doc: '_doc',
            primaryKeys: [],
            parititionColumns: ['date', 'projectId'],
            memo: '',
            draft: true,
          },
          name: 'continentCode',
          displayName: 'continentCode',
          dataType: 'STRING',
        },
      },
      {
        createTime: 1591176360000,
        updateTime: 1604887714000,
        createUserId: 1,
        updateUserId: 1,
        createUserName: 'admin',
        updateUserName: 'admin',
        projectId: '0',
        id: 88,
        displayName: '国家',
        eventId: 0,
        tableSchema: {
          createTime: 1579057522000,
          updateTime: 1600762999000,
          createUserId: 1,
          updateUserId: 1,
          projectId: '0',
          id: 5,
          table: {
            createTime: 1579056849000,
            updateTime: 1618213380000,
            createUserId: 1,
            updateUserId: 1,
            projectId: '0',
            id: 31,
            tableType: 'HIVE',
            name: 'event',
            displayName: '全局事件表',
            database: 'wolf',
            doc: '_doc',
            primaryKeys: [],
            parititionColumns: ['date', 'projectId'],
            memo: '',
            draft: true,
          },
          name: 'country',
          displayName: '国家',
          dataType: 'STRING',
        },
      },
      {
        createTime: 1591176373000,
        updateTime: 1604887714000,
        createUserId: 1,
        updateUserId: 1,
        createUserName: 'admin',
        updateUserName: 'admin',
        projectId: '0',
        id: 89,
        displayName: '设备ID',
        eventId: 0,
        tableSchema: {
          createTime: 1579057522000,
          updateTime: 1600745676000,
          createUserId: 1,
          updateUserId: 1,
          projectId: '0',
          id: 6,
          table: {
            createTime: 1579056849000,
            updateTime: 1618213380000,
            createUserId: 1,
            updateUserId: 1,
            projectId: '0',
            id: 31,
            tableType: 'HIVE',
            name: 'event',
            displayName: '全局事件表',
            database: 'wolf',
            doc: '_doc',
            primaryKeys: [],
            parititionColumns: ['date', 'projectId'],
            memo: '',
            draft: true,
          },
          name: 'deviceId',
          displayName: '设备ID',
          dataType: 'STRING',
        },
      },
      {
        createTime: 1591176389000,
        updateTime: 1604887714000,
        createUserId: 1,
        updateUserId: 1,
        createUserName: 'admin',
        updateUserName: 'admin',
        projectId: '0',
        id: 90,
        displayName: '下载渠道',
        eventId: 0,
        tableSchema: {
          createTime: 1579057522000,
          updateTime: 1590130558000,
          createUserId: 1,
          updateUserId: 1,
          projectId: '0',
          id: 7,
          table: {
            createTime: 1579056849000,
            updateTime: 1618213380000,
            createUserId: 1,
            updateUserId: 1,
            projectId: '0',
            id: 31,
            tableType: 'HIVE',
            name: 'event',
            displayName: '全局事件表',
            database: 'wolf',
            doc: '_doc',
            primaryKeys: [],
            parititionColumns: ['date', 'projectId'],
            memo: '',
            draft: true,
          },
          name: 'downloadChannel',
          displayName: 'downloadChannel',
          dataType: 'STRING',
        },
      },
      {
        createTime: 1591176409000,
        updateTime: 1604887714000,
        createUserId: 1,
        updateUserId: 1,
        createUserName: 'admin',
        updateUserName: 'admin',
        projectId: '0',
        id: 91,
        displayName: '事件名称',
        eventId: 0,
        tableSchema: {
          createTime: 1579057522000,
          updateTime: 1590130558000,
          createUserId: 1,
          updateUserId: 1,
          projectId: '0',
          id: 8,
          table: {
            createTime: 1579056849000,
            updateTime: 1618213380000,
            createUserId: 1,
            updateUserId: 1,
            projectId: '0',
            id: 31,
            tableType: 'HIVE',
            name: 'event',
            displayName: '全局事件表',
            database: 'wolf',
            doc: '_doc',
            primaryKeys: [],
            parititionColumns: ['date', 'projectId'],
            memo: '',
            draft: true,
          },
          name: 'eventName',
          displayName: 'eventName',
          dataType: 'STRING',
        },
      },
      {
        createTime: 1591176425000,
        updateTime: 1604887714000,
        createUserId: 1,
        updateUserId: 1,
        createUserName: 'admin',
        updateUserName: 'admin',
        projectId: '0',
        id: 92,
        displayName: '事件时间',
        eventId: 0,
        tableSchema: {
          createTime: 1579057523000,
          updateTime: 1608867054000,
          createUserId: 1,
          updateUserId: 1,
          projectId: '0',
          id: 10,
          table: {
            createTime: 1579056849000,
            updateTime: 1618213380000,
            createUserId: 1,
            updateUserId: 1,
            projectId: '0',
            id: 31,
            tableType: 'HIVE',
            name: 'event',
            displayName: '全局事件表',
            database: 'wolf',
            doc: '_doc',
            primaryKeys: [],
            parititionColumns: ['date', 'projectId'],
            memo: '',
            draft: true,
          },
          name: 'eventTime',
          displayName: 'eventTime',
          dataType: 'TIMESTAMP',
        },
      },
    ],
    number: 0,
    size: 10,
    totalElements: 36,
  },
}

const groupList = [
  {
    createTime: 1617002339000,
    updateTime: 1630656216000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: 'analyzer',
    updateUserName: 'analyzer',
    projectId: 'qvAD1jk8q0hA0Oxm',
    id: 873,
    name: '理财购买高响应用户',
    customerCount: 265,
    status: 'NORMAL',
    calcStatus: 'SUC',
    calcMemo: '',
    type: 'CONDITIONAL',
    lastCalcTime: 1630656180000,
    calcRule: 'ONCE',
    scheduleConf: { calcRule: 'ONCE' },
    connector: 'AND',
    whetherTest: true,
  },
  {
    createTime: 1617181139000,
    updateTime: 1617181279000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: 'analyzer',
    updateUserName: 'analyzer',
    projectId: 'qvAD1jk8q0hA0Oxm',
    id: 915,
    name: '营销高响应用户',
    customerCount: 1,
    status: 'NORMAL',
    calcStatus: 'SUC',
    calcMemo: '',
    type: 'UPLOAD',
    lastCalcTime: 1617181260000,
    calcRule: 'ONCE',
    scheduleConf: { calcRule: 'ONCE' },
    display: true,
    uploadPath: 'tmp/185391af-039e-4bc4-b089-29f4466b1951.csv',
    whetherTest: true,
  },
  {
    createTime: 1617181159000,
    updateTime: 1617181459000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: 'analyzer',
    updateUserName: 'analyzer',
    projectId: 'qvAD1jk8q0hA0Oxm',
    id: 916,
    name: '高价值客户',
    customerCount: 7742,
    status: 'NORMAL',
    calcStatus: 'SUC',
    calcMemo: '',
    type: 'COMPLEX',
    lastCalcTime: 1617181440000,
    calcRule: 'ONCE',
    scheduleConf: { calcRule: 'ONCE' },
    display: true,
    limits: 0,
    whetherTest: true,
  },
  {
    createTime: 1617180801000,
    updateTime: 1617180913000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: 'analyzer',
    updateUserName: 'analyzer',
    projectId: 'qvAD1jk8q0hA0Oxm',
    id: 914,
    name: '贷款高意愿客户',
    customerCount: 7742,
    status: 'NORMAL',
    calcStatus: 'SUC',
    calcMemo: '',
    type: 'CONDITIONAL',
    lastCalcTime: 1617180900000,
    calcRule: 'ONCE',
    scheduleConf: { calcRule: 'ONCE' },
    connector: 'AND',
    display: true,
    whetherTest: true,
  },
  {
    createTime: 1626660355000,
    updateTime: 1626660545000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: 'analyzer',
    updateUserName: 'analyzer',
    projectId: 'qvAD1jk8q0hA0Oxm',
    id: 1064,
    name: '活动敏感用户',
    customerCount: 265,
    status: 'NORMAL',
    calcStatus: 'SUC',
    calcMemo: '',
    type: 'CONDITIONAL',
    lastCalcTime: 1626660540000,
    calcRule: 'ONCE',
    scheduleConf: { calcRule: 'ONCE' },
    connector: 'AND',
    display: true,
    whetherTest: true,
  },
]
const demoValue = {
  connector: 'AND',
  filters: [
    {
      connector: 'AND',
      filters: [
        {
          action: 'DONE',
          eventInfo: {
            id: 331,
            eventType: 'BURIED_POINT_EVENT',
            displayName: '薪资到账',
            eventNameValue: '薪资到账',
            specialPropertyMappingList: [],
          },
          eventAggregateProperty: {
            propertyType: 'TIMES',
            property: {},
            fun: 'COUNT',
            operator: 'GT',
            value: 10,
          },
          eventFilterProperty: null,
          firstAction: 'DONE',
          firstTimeValue: 1,
          firstTimeUnit: 'HOUR',
          lastTimeValue: 1,
          lastTimeUnit: 'HOUR',
          todayDoEvents: [],
          pushData: false,
        },
      ],
    },
    {
      connector: 'AND',
      filters: [
        {
          action: 'FIRST_DO',
          eventInfo: {
            id: 328,
            eventType: 'BURIED_POINT_EVENT',
            displayName: '社保资金转入',
            eventNameValue: '转账',
            specialPropertyMappingList: [],
          },
          eventAggregateProperty: {},
          eventFilterProperty: {
            connector: 'AND',
            filters: [
              {
                connector: 'AND',
                filters: [
                  {
                    tableId: 31,
                    schemaId: 4621,
                    field: 'bool',
                    fieldName: '布尔',
                    fieldType: 'BOOL',
                    level1: '通用事件属性',
                    level2: '',
                    operator: 'IS_TRUE',
                    value: null,
                    showValue: null,
                    isEnum: false,
                  },
                ],
              },
              {
                connector: 'AND',
                filters: [
                  {
                    tableId: 31,
                    schemaId: 3814,
                    field: 'day',
                    fieldName: 'day',
                    fieldType: 'TIMESTAMP',
                    level1: '通用事件属性',
                    level2: '',
                    operator: 'EQ',
                    value: 1754537958000,
                    showValue: null,
                    isEnum: false,
                  },
                ],
              },
              {
                connector: 'AND',
                filters: [
                  {
                    tableId: 31,
                    schemaId: 3814,
                    field: 'day',
                    fieldName: 'day',
                    fieldType: 'TIMESTAMP',
                    level1: '通用事件属性',
                    level2: '',
                    operator: 'ADVANCED_BETWEEN',
                    value: [
                      {
                        type: 'ABSOLUTE',
                        times: 1,
                        timeTerm: 'DAY',
                        isPast: true,
                        timestamp: 1754019613046,
                      },
                      {
                        type: 'NOW',
                        times: 1,
                        timeTerm: 'DAY',
                        isPast: true,
                        timestamp: 1756179619845,
                      },
                    ],
                    showValue: null,
                    isEnum: false,
                  },
                ],
              },
            ],
          },
          firstAction: 'FIRST_DO_LAST_NOT_DO',
          firstTimeValue: 1,
          firstTimeUnit: 'HOUR',
          lastTimeValue: 10,
          lastTimeUnit: 'HOUR',
          lastAction: 'NOT_DO',
          lastEventInfo: {
            id: 321,
            eventType: 'BURIED_POINT_EVENT',
            displayName: '购买指定产品',
            eventNameValue: '购买产品A',
            specialPropertyMappingList: [],
          },
          lastEventFilterProperty: {
            connector: 'AND',
            filters: [
              {
                connector: 'AND',
                filters: [
                  {
                    tableId: 31,
                    schemaId: 21,
                    field: 'region',
                    fieldName: '地区',
                    fieldType: 'STRING',
                    level1: '通用事件属性',
                    level2: '',
                    operator: 'EQ',
                    value: '淄博',
                    showValue: null,
                    isEnum: false,
                  },
                ],
              },
            ],
          },
          todayDoEvents: [],
          pushData: true,
        },
      ],
    },
    {
      connector: 'AND',
      filters: [
        {
          action: 'DO_SEQ',
          eventInfo: {
            id: 333,
            eventType: 'BURIED_POINT_EVENT',
            displayName: '公金',
            eventNameValue: '公金',
            specialPropertyMappingList: [
              {
                displayName: '公金',
                propertySchema: '公金',
                dataType: 'TIMESTAMP',
                index: 0,
              },
            ],
          },
          eventAggregateProperty: {},
          eventFilterProperty: null,
          firstAction: 'DO_SEQ',
          firstTimeValue: 1,
          firstTimeUnit: 'HOUR',
          lastTimeValue: 1,
          lastTimeUnit: 'HOUR',
          todayDoEvents: [
            {
              eventInfo: {
                id: 331,
                eventType: 'BURIED_POINT_EVENT',
                displayName: '薪资到账',
                eventNameValue: '薪资到账',
                specialPropertyMappingList: [],
              },
              eventFilterProperty: {
                connector: 'AND',
                filters: [
                  {
                    connector: 'AND',
                    filters: [
                      {
                        tableId: 31,
                        schemaId: 4621,
                        field: 'bool',
                        fieldName: '布尔',
                        fieldType: 'BOOL',
                        level1: '通用事件属性',
                        level2: '',
                        operator: 'IS_TRUE',
                        value: null,
                        showValue: null,
                        isEnum: false,
                      },
                    ],
                  },
                ],
              },
            },
            {
              eventInfo: {
                id: 328,
                eventType: 'BURIED_POINT_EVENT',
                displayName: '社保资金转入',
                eventNameValue: '转账',
                specialPropertyMappingList: [],
              },
              eventFilterProperty: {
                connector: 'AND',
                filters: [
                  {
                    connector: 'AND',
                    filters: [
                      {
                        tableId: 31,
                        schemaId: 3814,
                        field: 'day',
                        fieldName: 'day',
                        fieldType: 'TIMESTAMP',
                        level1: '通用事件属性',
                        level2: '',
                        operator: 'EQ',
                        value: 1754883430000,
                        showValue: null,
                        isEnum: false,
                      },
                    ],
                  },
                ],
              },
            },
          ],
          pushData: false,
        },
      ],
    },
  ],
}

const demoValueMinuteOrHour = {
  connector: 'AND',
  filters: [
    {
      connector: 'AND',
      eventGroup: {
        connector: 'OR',
        filters: [
          {
            connector: 'AND',
            filters: [
              {
                action: 'DONE',
                eventInfo: {
                  id: 1,
                  displayName: '页面浏览',
                },
                eventAggregateProperty: {
                  propertyType: 'TIMES',
                  property: {},
                  fun: 'COUNT',
                  operator: 'EQ',
                  value: 3,
                },
                dateRange: [
                  {
                    type: 'RELATIVE',
                    timeTerm: 'MINUTE',
                    isPast: true,
                    times: 7,
                    truncateAsDay: true,
                  },
                  {
                    type: 'NOW',
                    timeTerm: 'MINUTE',
                    isPast: true,
                    isEndTime: true,
                  },
                ],
                eventFilterProperty: {
                  connector: 'AND',
                  filters: [
                    {
                      connector: 'AND',
                      filters: [
                        {
                          field: 'city',
                          fieldName: '城市',
                          fieldType: 'STRING',
                          level1: '用户属性',
                          level2: '地理信息',
                          operator: 'EQ',
                          value: '长沙',
                          showValue: null,
                          isEnum: true,
                        },
                      ],
                    },
                    {
                      connector: 'AND',
                      filters: [
                        {
                          field: 'product_name',
                          fieldName: '产品名称',
                          fieldType: 'STRING',
                          level1: '用户属性',
                          level2: '产品信息',
                          operator: 'EQ',
                          value: '理财5号',
                          showValue: null,
                          isEnum: true,
                        },
                      ],
                    },
                  ],
                },
              },
            ],
          },
          {
            connector: 'AND',
            filters: [
              {
                action: 'DONE',
                eventInfo: {
                  id: 4,
                  displayName: '商品购买',
                },
                eventAggregateProperty: {
                  propertyType: 'TIMES',
                  property: {},
                  fun: 'COUNT',
                  operator: 'EQ',
                  value: 1,
                },
                dateRange: [
                  {
                    type: 'RELATIVE',
                    timeTerm: 'MINUTE',
                    isPast: true,
                    times: 7,
                    truncateAsDay: true,
                  },
                  {
                    type: 'NOW',
                    timeTerm: 'MINUTE',
                    isPast: true,
                    isEndTime: true,
                  },
                ],
                eventFilterProperty: null,
              },
            ],
          },
        ],
      },
    },
    {
      connector: 'AND',
      segment: {
        connector: 'AND',
        filters: [
          {
            connector: 'AND',
            filters: [
              {
                type: 'INCLUDE',
                segment: {
                  id: 873,
                  name: '理财购买高响应用户',
                  lastCalcTime: 1630656180000,
                  customerCount: 265,
                },
              },
            ],
          },
        ],
      },
    },
  ],
}

/**
 * demoValue1 是给正式页面模拟用的, 这个是在test调试夜场满上用的
 * 所有组件都加上 用来测试的数据
 */
const demoValue2 = {
  connector: 'AND',
  filters: [
    {
      connector: 'AND',
      eventGroup: {
        connector: 'OR',
        filters: [
          {
            connector: 'AND',
            filters: [
              {
                action: 'DONE',
                eventInfo: {
                  id: 6,
                  eventType: 'PRODUCT_EVENT',
                  displayName: '添加到购物车',
                  eventNameValue: '添加到购物车',
                },
                eventAggregateProperty: {
                  propertyType: 'TIMES',
                  property: {},
                  fun: 'COUNT',
                  operator: 'EQ',
                  value: 1,
                },
                eventFilterProperty: {},
                firstAction: 'DONE',
                firstTimeValue: 2,
                firstTimeUnit: 'HOUR',
                lastTimeValue: 1,
                lastTimeUnit: 'HOUR',
                todayDoEvents: [],
              },
            ],
          },
          {
            connector: 'AND',
            filters: [
              {
                action: 'FIRST_DO',
                eventInfo: {
                  id: 1,
                  eventType: 'USER_ACTION',
                  displayName: '登录',
                  eventNameValue: '登录',
                },
                eventAggregateProperty: {},
                eventFilterProperty: {
                  connector: 'AND',
                  filters: [
                    {
                      connector: 'AND',
                      filters: [
                        {
                          tableId: 31,
                          schemaId: 4621,
                          field: 'bool',
                          fieldName: '布尔',
                          fieldType: 'BOOL',
                          level1: '通用事件属性',
                          level2: '',
                          operator: 'IS_TRUE',
                          value: null,
                          showValue: null,
                          isEnum: false,
                        },
                      ],
                    },
                    {
                      connector: 'AND',
                      filters: [
                        {
                          tableId: 31,
                          schemaId: 3814,
                          field: 'day',
                          fieldName: 'day',
                          fieldType: 'TIMESTAMP',
                          level1: '通用事件属性',
                          level2: '',
                          operator: 'EQ',
                          value: 1755240976000,
                          showValue: null,
                          isEnum: false,
                        },
                      ],
                    },
                  ],
                },
                firstAction: 'FIRST_DO_LAST_NOT_DO',
                firstTimeValue: 1,
                firstTimeUnit: 'HOUR',
                lastTimeValue: 1,
                lastTimeUnit: 'HOUR',
                lastAction: 'NOT_DO',
                lastEventInfo: {
                  id: 7,
                  eventType: 'TRANSACTION_EVENT',
                  displayName: '下单',
                  eventNameValue: '下单',
                },
                lastEventFilterProperty: {
                  connector: 'AND',
                  filters: [
                    {
                      connector: 'AND',
                      filters: [
                        {
                          tableId: 31,
                          schemaId: 21,
                          field: 'region',
                          fieldName: '地区',
                          fieldType: 'STRING',
                          level1: '通用事件属性',
                          level2: '',
                          operator: 'EQ',
                          value: '北京',
                          showValue: null,
                          isEnum: false,
                        },
                      ],
                    },
                  ],
                },
                todayDoEvents: [],
              },
            ],
          },
          {
            connector: 'AND',
            filters: [
              {
                action: 'DO_SEQ',
                eventInfo: {
                  id: 1,
                  eventType: 'USER_ACTION',
                  displayName: '登录',
                  eventNameValue: '登录',
                },
                eventAggregateProperty: {},
                eventFilterProperty: null,
                firstAction: 'DO_SEQ',
                firstTimeValue: 3,
                firstTimeUnit: 'HOUR',
                lastTimeValue: 1,
                lastTimeUnit: 'HOUR',
                todayDoEvents: [
                  {
                    eventInfo: {
                      id: 2,
                      eventType: 'USER_ACTION',
                      displayName: '注册',
                      eventNameValue: '注册',
                    },
                    eventFilterProperty: {
                      connector: 'AND',
                      filters: [
                        {
                          connector: 'AND',
                          filters: [
                            {
                              tableId: 31,
                              schemaId: 4621,
                              field: 'bool',
                              fieldName: '布尔',
                              fieldType: 'BOOL',
                              level1: '通用事件属性',
                              level2: '',
                              operator: 'IS_TRUE',
                              value: null,
                              showValue: null,
                              isEnum: false,
                            },
                          ],
                        },
                      ],
                    },
                  },
                  {
                    eventInfo: {
                      id: 7,
                      eventType: 'TRANSACTION_EVENT',
                      displayName: '下单',
                      eventNameValue: '下单',
                    },
                    eventFilterProperty: {
                      connector: 'AND',
                      filters: [
                        {
                          connector: 'AND',
                          filters: [
                            {
                              tableId: 31,
                              schemaId: 4621,
                              field: 'bool',
                              fieldName: '布尔',
                              fieldType: 'BOOL',
                              level1: '通用事件属性',
                              level2: '',
                              operator: 'IS_TRUE',
                              value: null,
                              showValue: null,
                              isEnum: false,
                            },
                          ],
                        },
                      ],
                    },
                  },
                  {
                    eventInfo: {
                      id: 8,
                      eventType: 'TRANSACTION_EVENT',
                      displayName: '支付',
                      eventNameValue: '支付',
                    },
                    eventFilterProperty: {
                      connector: 'AND',
                      filters: [
                        {
                          connector: 'AND',
                          filters: [
                            {
                              tableId: 31,
                              schemaId: 4621,
                              field: 'bool',
                              fieldName: '布尔',
                              fieldType: 'BOOL',
                              level1: '通用事件属性',
                              level2: '',
                              operator: 'IS_TRUE',
                              value: null,
                              showValue: null,
                              isEnum: false,
                            },
                          ],
                        },
                      ],
                    },
                  },
                ],
              },
            ],
          },
        ],
      },
      userLabel: {
        connector: 'AND',
        filters: [
          {
            connector: 'AND',
            filters: [
              {
                id: '579',
                label: '自动标签_TIMESTAMP_2021_09_14',
                displayName: '自动标签_TIMESTAMP_2021_09_14',
                operator: 'LIKE',
                value: [
                  '1',
                  '2',
                  '3',
                ],
                fieldType: 'STRING',
                dateType: 'ABSOLUTE',
                showValue: [
                  '1',
                  '2',
                  '3',
                ],
                checkUserTag: false,
                timeType: 'DAY',
                exCalendar: {},
                times: 1750666252039,
              },
            ],
          },
        ],
      },
    },
    {
      connector: 'AND',
      segment: {
        connector: 'AND',
        filters: [
          {
            connector: 'AND',
            filters: [
              {
                type: 'INCLUDE',
                segment: {
                  id: 873,
                  name: '理财购买高响应用户',
                  lastCalcTime: 1630656180000,
                  customerCount: 265,
                },
              },
            ],
          },
        ],
      },
    },
    {
      connector: 'AND',
      userLabel: {
        connector: 'AND',
        filters: [
          {
            connector: 'AND',
            filters: [
              {
                id: '578',
                label: '自动标签_STRING_2021_09_14',
                displayName: '自动标签_STRING_2021_09_14',
                operator: 'EQ',
                value: '9',
                fieldType: 'STRING',
                dateType: 'RELATIVE',
                showValue: '9',
                checkUserTag: false,
                timeType: 'DAY',
                exCalendar: {},
                times: 66,
              },
            ],
          },
        ],
      },
    },
  ],
}

const mockEventDataProvider = {
  getEventList: async (searchText: string) => {
    window.console.log('调用了getEventList接口')
    // 模拟事件列表数据，包含两层和一层的结构
    const allEvents = [
      // 两层事件 - 有分类
      {
        id: 3,
        name: '页面浏览',
        eventNameValue: '页面浏览',
        eventType: 'PAGE_EVENT',
        categoryId: 100,
        categoryName: '页面行为',
        displayName: '页面浏览',
      },
      {
        id: 4,
        name: '页面停留',
        eventNameValue: '页面停留',
        eventType: 'PAGE_EVENT',
        categoryId: 100,
        categoryName: '页面行为',
        displayName: '页面停留',
      },
      {
        id: 5,
        name: '商品浏览',
        eventNameValue: '商品浏览',
        eventType: 'PRODUCT_EVENT',
        categoryId: 200,
        categoryName: '商品行为',
        displayName: '商品浏览',
      },
      {
        id: 6,
        name: '添加到购物车添加到购物车添加到购物车添加到购物车',
        eventNameValue: '添加到购物车11111',
        eventType: 'PRODUCT_EVENT',
        categoryId: 200,
        categoryName: '商品行为',
        displayName: '添加到购物车11111',
      },
      {
        id: 7,
        name: '下单',
        eventNameValue: '下单',
        eventType: 'TRANSACTION_EVENT',
        categoryId: 300,
        categoryName: '交易行为',
        displayName: '下单',
      },
      {
        id: 8,
        name: '支付',
        eventNameValue: '支付',
        eventType: 'TRANSACTION_EVENT',
        categoryId: 300,
        categoryName: '交易行为',
        displayName: '支付',
      },
      {
        id: 1,
        name: '登录登录登录登录登录登录登录登录登录登录登录登录登录登录',
        eventNameValue: '登录',
        eventType: 'USER_ACTION',
        categoryId: null,
        displayName: '登录',
      },
      {
        id: 2,
        name: '注册',
        eventNameValue: '注册',
        eventType: 'USER_ACTION',
        categoryId: null,
        displayName: '注册',
      },
    ]

    // 按搜索词过滤事件（支持事件名和分类名搜索）
    let filteredEvents = allEvents
    if (searchText && searchText.trim()) {
      const searchLower = searchText.toLowerCase()
      filteredEvents = allEvents.filter(event =>
        event.name.toLowerCase().includes(searchLower)
        || (event.categoryName && event.categoryName.toLowerCase().includes(searchLower)),
      )
    }

    return {
      content: filteredEvents,
    }
  },
  getEventPropertyList: async (_searchText, _eventId) => {
    // 模拟事件属性列表数据
    return [
      {
        field: 'user_id',
        fieldName: '用户ID',
        fieldType: 'STRING',
        level1: '用户属性',
        level2: '基础信息',
        isEnum: false,
      },
      {
        field: 'user_id',
        fieldName: '商品名称',
        fieldType: 'STRING',
        level1: '事件属性',
        level2: '基础信息',
        isEnum: false,
      },
      {
        field: 'age',
        fieldName: '年龄',
        fieldType: 'INT',
        level1: '用户属性',
        level2: '基础信息',
        isEnum: false,
      },
      {
        field: 'city',
        fieldName: '城市',
        fieldType: 'STRING',
        level1: '用户属性',
        level2: '地理信息',
        isEnum: true,
      },
    ]
  },
  getPropertyList: async (_name) => {
    // 模拟属性列表数据
    return propertyList
  },
  getEventCountLogsByProjectId: async () => {
    // 模拟事件统计数据 , 这里正常是要传递projectId
    return eventCount
  },
  getPropertyEnumList: async (_tableId, _schemaId) => {
    return propertyItemList
  },
  // 新增分类查询接口
  findCategoryByProjectId: async () => {
    return [
      {
        id: 100,
        name: '页面行为',
        path: '0,',
      },
      {
        id: 200,
        name: '商品行为',
        path: '0,',
      },
      {
        id: 300,
        name: '交易行为',
        path: '0,',
      },
    ]
  },
  // 模拟获取分群列表
  getSegmentList: async (searchText: string) => {
    const segments = groupList
    if (!searchText)
      return segments
    const filtered = segments.filter(item => item.name.toLowerCase().includes(searchText.toLowerCase()))
    return filtered
  },

  getGroupList: async () => {
    return groupList
  },
}

export {
  demoValue,
  demoValue2,
  demoValueMinuteOrHour,
  eventCount,
  eventDatas,
  eventProperty,
  functions,
  mockEventDataProvider,
  propertyItemList,
  propertyList,
  tableId,
}

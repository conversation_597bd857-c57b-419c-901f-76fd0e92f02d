<script>
import _ from 'lodash'
import { HandIcon } from '@/components/icons'

// 工具函数：查找树节点
function findObj(data, key) {
  let info = {}
  data.forEach((n) => {
    if (n.key === key) {
      info = n
    }
    if (n.children) {
      n.children.forEach((w) => {
        if (w.key === key) {
          info = w
        }
      })
    }
  })
  return info
}

// 构建树形结构
function buildEventTree(events, categoryList) {
  const treeData = []
  const expandedKeys = []
  const categoryObj = {}

  // 构建分类映射
  categoryList.forEach((category) => {
    categoryObj[`key.${category.id}`] = category
  })

  events.forEach((event) => {
    if (!event.categoryId) {
      // 一层事件：直接添加到根级别
      treeData.push({
        title: event.name,
        key: `event_${event.id}`,
        isLeaf: true,
        eventData: event,
      })
    }
    else {
      // 二层事件：需要先创建分类节点
      const categoryKey = `key.${event.categoryId}`
      const categoryInfo = categoryObj[categoryKey]

      if (categoryInfo) {
        // 检查分类节点是否已存在
        if (!treeData.find(item => item.key === categoryKey)) {
          treeData.push({
            title: categoryInfo.name,
            key: categoryKey,
            isLeaf: false,
            children: [],
          })
          expandedKeys.push(categoryKey)
        }

        // 找到分类节点并添加事件
        const categoryNode = findObj(treeData, categoryKey)
        if (!categoryNode.children) {
          categoryNode.children = []
        }
        categoryNode.children.push({
          title: event.name,
          key: `event_${event.id}`,
          isLeaf: true,
          eventData: event,
        })
      }
    }
  })

  return { treeData, expandedKeys }
}

export default {
  name: 'FilterEventFieldSelect',
  components: {
    HandIcon,
  },
  inject: ['filterContext'],
  props: {
    value: {
      type: Object,
      required: true,
    },
    onChange: {
      type: Function,
      default: () => {},
    },
    type: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      treeData: [],
      visible: false,
      searchValue: '',
      selectedKeys: [],
      expandedKeys: [],
      loading: false,
      eventMap: {},
      displayName: '',
      isOnFocus: false,
      showClearIcon: false,
    }
  },
  computed: {
    context() {
      return this.filterContext()
    },
    dataProvider() {
      return this.context.dataProvider || {}
    },
    currentEventInfo() {
      return this.type === 'last' ? this.value?.lastEventInfo : this.value?.eventInfo
    },
    currentEventId() {
      return this.currentEventInfo?.id
    },
    currentEventDisplayName() {
      return this.currentEventInfo?.displayName || '请选择事件'
    },
  },
  watch: {
    currentEventDisplayName(newValue) {
      if (!this.isOnFocus) {
        this.searchValue = this.currentEventId ? newValue || '' : ''
      }
    },
    currentEventId(newId) {
      this.selectedKeys = newId ? [`event_${newId}`] : []
    },
    displayName: {
      handler() {
        this.init()
      },
      immediate: false,
    },
  },
  mounted() {
    this.searchValue = this.currentEventId ? this.currentEventDisplayName : ''
    this.selectedKeys = this.currentEventId ? [`event_${this.currentEventId}`] : []
    this.init()
  },
  methods: {
    async init() {
      this.loading = true
      try {
        // 根据搜索词获取事件列表，初始时获取所有事件
        const searchTerm = this.displayName || ''
        const eventResult = await this.dataProvider.getEventList(searchTerm)
        const events = eventResult.content || []

        // 获取分类列表
        const categoryList = await this.dataProvider.findCategoryByProjectId()

        // 构建树形结构
        const result = buildEventTree(events, categoryList || [])
        this.treeData = result.treeData
        this.expandedKeys = result.expandedKeys

        // 构建事件映射
        const eventMap = {}
        events.forEach((event) => {
          eventMap[event.id] = event
        })
        this.eventMap = eventMap
      }
      catch (error) {
        console.warn('Failed to fetch event list:', error)
        this.treeData = []
        this.expandedKeys = []
        this.eventMap = {}
      }
      this.loading = false
    },

    onSelect(selectedKeys, info) {
      if (info.node.isLeaf && selectedKeys.length > 0) {
        const eventId = selectedKeys[0].replace('event_', '')
        const selectedEvent = this.eventMap[eventId] || info.node.eventData

        if (selectedEvent) {
          this.selectedKeys = selectedKeys
          // 选择后关闭下拉框并恢复显示
          this.visible = false
          this.isOnFocus = false
          this.searchValue = selectedEvent.name
          this.displayName = ''
          this.onEventFilterChange(selectedEvent.id)
        }
      }
    },

    onEventFilterChange(eventId) {
      const currentEvent = this.eventMap[eventId]
      if (!currentEvent)
        return

      const eventInfoProperty = this.type === 'last' ? 'lastEventInfo' : 'eventInfo'

      this.value.changeProperty({
        ...this.value,
        [eventInfoProperty]: {
          id: currentEvent.id,
          eventType: currentEvent.eventType,
          displayName: currentEvent.name,
          eventNameValue: currentEvent.eventNameValue,
          filter: currentEvent.filter,
          specialPropertyMappingList: currentEvent.specialPropertyMappingList,
        },
        eventAggregateProperty: {},
        eventFilterProperty: null,
      })
      this.onChange(this.value)
    },

    handleVisibleChange(visible) {
      this.visible = visible
      if (!visible) {
        // 关闭时恢复显示值 - 只有选中了事件才显示事件名
        this.searchValue = this.currentEventId ? (this.eventMap[this.currentEventId]?.name || this.currentEventDisplayName) : ''
        this.displayName = ''
        this.isOnFocus = false
      }
    },

    onExpand(expandedKeys) {
      this.expandedKeys = expandedKeys
    },

    onSearch: _.debounce(function (e) {
      const value = e.target.value
      this.searchValue = value
      this.displayName = value
      this.isOnFocus = true
      if (!this.visible) {
        this.visible = true
      }
    }, 300),

    onInputChange(e) {
      this.searchValue = e.target.value
      if (e.target.value === '') {
        this.onClear()
      }
    },

    onClear() {
      // 处理清除事件
      this.searchValue = ''
      this.displayName = ''
      this.selectedKeys = []
      // 清空选中的事件信息
      this.value.changeProperty({
        ...this.value,
        [this.type === 'last' ? 'lastEventInfo' : 'eventInfo']: undefined,
        eventAggregateProperty: {},
        eventFilterProperty: null,
      })
      this.onChange(this.value)
      // 清除后重新加载所有事件
      // this.init()
    },

    onFocus() {
      this.searchValue = ''
      this.displayName = ''
      this.isOnFocus = true
    },

    onBlur() {
      setTimeout(() => {
        this.isOnFocus = false
      }, 200)
    },

    onMouseEnter() {
      this.showClearIcon = true
    },

    onMouseLeave() {
      this.showClearIcon = false
    },
  },
}
</script>

<template>
  <div>
    <a-dropdown
      :trigger="['click']"
      :visible="visible"
      :get-popup-container="(triggerNode) => triggerNode.parentNode"
      @visibleChange="handleVisibleChange"
    >
      <div class="tree-select-trigger" :title="searchValue" @mouseenter="onMouseEnter" @mouseleave="onMouseLeave">
        <a-input
          :value="searchValue"
          :placeholder="currentEventId ? '' : currentEventDisplayName"
          :disabled="loading"
          @input="onSearch"
          @change="onInputChange"
          @focus="onFocus"
          @blur="onBlur"
        >
          <a-icon slot="suffix" type="down" class="dropdown-icon" />
        </a-input>

        <div
          v-if="showClearIcon && searchValue"
          class="clear-icon"
          @click.stop="onClear"
        >
          <a-icon type="close-circle" />
        </div>
      </div>

      <template #overlay>
        <div class="tree-select-panel">
          <div class="tree-container">
            <a-spin :spinning="loading">
              <a-tree
                :tree-data="treeData"
                :selected-keys="selectedKeys"
                :expanded-keys="expandedKeys"
                :height="300"
                @select="onSelect"
                @expand="onExpand"
              >
                <template #title="{ title, isLeaf, expanded }">
                  <HandIcon v-if="isLeaf" style="font-size: 14px; color: #86909C;" />
                  <a-icon v-else-if="expanded" slot="switcherIcon" type="folder-open" />
                  <a-icon v-else slot="switcherIcon" type="folder" />
                  <span :class="{ 'event-node': isLeaf, 'category-node': !isLeaf }" :title="title">
                    {{ title }}
                  </span>
                </template>
              </a-tree>
            </a-spin>
          </div>
        </div>
      </template>
    </a-dropdown>
  </div>
</template>

<style scoped lang="scss">
 :deep(.ant-input:not(:last-child)) {
  padding-right: 50px !important;
}
.tree-select-trigger {
  position: relative;
  cursor: pointer;
}

.dropdown-icon {
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
  cursor: pointer;
}

/* :deep(.ant-input) {
  padding-right: 54px !important;
} */

.clear-icon {
  position: absolute;
  right: 32px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  color: rgba(0, 0, 0, 0.25);
  font-size: 12px;
  z-index: 1;
  padding: 0 4px;
  display: flex;
  align-items: center;
}

.clear-icon:hover {
  color: rgba(0, 0, 0, 0.45);
}

.tree-select-panel {
  background: white;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 300px;
  max-height: 400px;
}

.tree-container {
  overflow-y: auto;
  overflow-x: hidden;
  padding: 8px;
  padding-right: 20px;
  max-height: 380px;
  background: #fafafa;
  width: 100%;
  box-sizing: border-box;
}

.category-node {
  font-weight: 600;
  color: #595959;
}

.event-node {
  color: #262626;
}

:deep(.ant-tree-node-selected) {
  background-color:#DCE4F1 !important;
  .event-node {
    color: #5478BA !important;
  }
}

:deep(.ant-tree-title) {
  width: 100%;
}

:deep(.ant-tree li .ant-tree-node-content-wrapper) {
  /* padding: 4px 8px; */
  border-radius: 4px;
  width: calc(100% - 16px) !important;
  overflow: hidden;
  /* display: flex !important; */
  align-items: center;
  position: relative;
  margin: 1px 0;
  transition: background-color 0.2s ease;
  &:hover {
    background-color:#DCE4F1 !important;
  }
}

:deep(.ant-tree-node-content-wrapper .ant-tree-title) {
  width: 100%;
  cursor: pointer;
}
</style>
